---
import { Picture } from "astro:assets";
import type { Sections } from "@/data/portfolio";

interface Props {
    data: Sections["profile"];
}

const {
    name = "",
    jobTitle = "",
    contacts = [],
    profileImage = "",
    researchFieldsTitle = "",
    researchFields = [],
} = Astro.props.data;
---

<section id="profile" class="pt-10 sm:pt-14 pb-10">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 justify-center">
        <!-- Right Column: Profile Information -->
        <div class="order-2">
            <h1 class="text-3xl sm:text-4xl lg:text-5xl text-sky-900 font-extrabold leading-tight">
                {name}
            </h1>
            <p class="mt-2 text-lg text-slate-600 italic">{jobTitle}</p>

            {
                contacts && contacts.length > 0 && (
                    <dl class="mt-6 grid grid-cols-1 gap-3" aria-label="Professional contact information">
                        {contacts.map((contact) => {
                            const IconComponent = contact.icon;
                            return (
                                <div class="bg-white border border-slate-200 rounded-lg px-4 py-3 shadow-sm">
                                    <dt class="sr-only">{contact.type}</dt>
                                    <dd class="flex items-center gap-3 text-sm font-medium">
                                        <IconComponent class="w-5 h-5 text-sky-600 flex-shrink-0" />
                                        <div>
                                            <span class="font-semibold text-sky-900">{contact.type}:</span>
                                            <span class="ml-2">{contact.value}</span>
                                        </div>
                                    </dd>
                                </div>
                            );
                        })}
                    </dl>
                )
            }

            {
                researchFields && researchFields.length > 0 && (
                    <div class="mt-6">
                        <h2 class="text-xl text-sky-900 font-bold">{researchFieldsTitle}</h2>
                        <ul class="mt-3 flex flex-wrap gap-2" role="list" aria-label="Research specialization tags">
                            {researchFields.map((field) => (
                                <li>
                                    <span class="inline-block bg-sky-50 text-sky-900 border border-sky-200 px-4 py-2 rounded-full text-sm font-medium hover:bg-sky-100 transition-colors">
                                        {field}
                                    </span>
                                </li>
                            ))}
                        </ul>
                    </div>
                )
            }
        </div>

        <!-- Left Column: Large Hero Profile Image -->
        <div class="order-1">
            <figure
                class="bg-transparent grid place-items-center shadow-xl max-w-80 md:max-w-[400px] md:mx-auto rounded-2xl overflow-hidden"
            >
                {
                    profileImage && (
                        <Picture
                            src={profileImage}
                            alt={`Profile picture of ${name}`}
                            width={400}
                            height={400}
                            loading="eager"
                            fetchpriority="high"
                        />
                    )
                }
            </figure>
        </div>
    </div>
</section>
