---
import Layout from "@/layouts/main.astro";
import Header from "@/components/layout/Header.astro";
import Hero from "@/components/sections/Hero.astro";
import PublicationsWithFilter from "@/components/publications/PublicationsWithFilter.vue";
import Experience from "@/components/sections/Experience.astro";
import Softwares from "@/components/sections/Softwares.astro";
import Footer from "@/components/layout/Footer.astro";
import { siteInfo, sections } from "@/data/portfolio";
---

<Layout title={siteInfo.title} bodyClass="bg-[#f3f6fb] text-slate-800">
    <Header data={siteInfo} />

    <main id="main" class="mx-auto max-w-6xl px-4 sm:px-6">
        <Hero data={sections.profile} />
        <PublicationsWithFilter data={sections.publications} client:load />
        <Experience data={sections.experiences} />
        <Softwares data={sections.softwares} />
    </main>

    <Footer data={siteInfo} />
</Layout>
