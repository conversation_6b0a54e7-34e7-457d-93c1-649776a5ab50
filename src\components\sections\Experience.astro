---
import type { Sections } from "@/data/portfolio";

interface Props {
    data: Sections["experiences"];
}

const {
    title,
    employments: employmentData,
    qualifications: qualificationData,
    certifications: certificationData,
} = Astro.props.data;

const { title: employmentTitle, employments } = employmentData;
const { title: qualificationTitle, qualifications } = qualificationData;
const { title: certificationTitle, certifications } = certificationData;
---

<section id="experience" aria-labelledby="exp-title" class="py-10 border-t border-slate-200">
    <h2 id="exp-title" class="text-2xl text-sky-900 font-extrabold">{title}</h2>

    <div class="mt-6 space-y-8">
        <!-- Employment Experience -->
        {
            employments && employments.length > 0 && (
                <section aria-labelledby="employment-title">
                    <h3 id="employment-title" class="text-sky-900 font-bold">
                        {employmentTitle}
                    </h3>
                    <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
                        {employments.map((job) => (
                            <article class="bg-white border border-slate-200 rounded p-4 flex flex-col h-full">
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0" set:html={job.logo} />

                                    <div class="flex-1 min-w-0">
                                        {/* PRIMARY DATA: Institution/Organization name + Position title (bold and prominent) */}
                                        <h4 class="text-sky-900 font-bold text-lg mb-1">{job.organization}</h4>
                                        <h5 class="text-sky-900 font-bold text-base mb-2">{job.position}</h5>

                                        {/* SECONDARY DATA: Location, Years, Employment type (smaller and less prominent) */}
                                        <div class="text-sm text-slate-600 mb-2 space-y-1">
                                            <p>
                                                <span class="font-medium">Period:</span> {job.period}
                                            </p>
                                            <p>
                                                <span class="font-medium">Location:</span> {job.location}
                                            </p>
                                            <p>
                                                <span class="font-medium">Employment Type:</span> {job.employmentType}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Academic Qualifications -->
        {
            qualifications && qualifications.length > 0 && (
                <section aria-labelledby="degrees-title" class="border-t border-slate-100 pt-6">
                    <h3 id="degrees-title" class="text-sky-900 font-bold">
                        {qualificationTitle}
                    </h3>
                    <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
                        {qualifications.map((degree) => (
                            <article class="bg-white border border-slate-200 rounded p-4 flex flex-col h-full">
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0" set:html={degree.logo} />

                                    <div class="flex-1 min-w-0">
                                        {/* PRIMARY DATA: Degree name + University name (bold and prominent) */}
                                        <h4 class="text-sky-900 font-bold text-lg mb-1">{degree.degree}</h4>
                                        <h5 class="text-sky-900 font-bold text-base mb-2">{degree.university}</h5>

                                        {/* SECONDARY DATA: Year completed, intake year, additional details (smaller and less prominent) */}
                                        <div class="text-sm text-slate-600 mb-2 space-y-1">
                                            {degree.yearCompleted && (
                                                <p>
                                                    <span class="font-medium">Year Completed: </span>
                                                    {degree.yearCompleted}
                                                </p>
                                            )}
                                            <p>
                                                <span class="font-medium">Type:</span> {degree.type}
                                            </p>
                                        </div>

                                        {degree.details && (
                                            <div class="text-sm text-slate-600 mt-2">
                                                <p>{degree.details}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Professional Certifications -->
        {
            certifications && certifications.length > 0 && (
                <section aria-labelledby="certs-title" class="border-t border-slate-100 pt-6">
                    <h3 id="certs-title" class="text-sky-900 font-bold">
                        {certificationTitle}
                    </h3>
                    <div class="mt-3 grid gap-4 md:grid-cols-2 md:gap-6">
                        {certifications.map((cert) => {
                            const IconComponent = cert.icon;
                            return (
                                <article class="bg-white border border-slate-200 rounded p-4 flex flex-col h-full">
                                    <div class="flex items-start gap-4">
                                        <div class="flex-shrink-0 mt-1">
                                            <IconComponent class="w-6 h-6 text-sky-600" />
                                        </div>

                                        <div class="flex-1 min-w-0">
                                            {/* PRIMARY DATA: Certification title + Year (bold and prominent) */}
                                            <h4 class="text-sky-900 font-bold text-lg mb-1">{cert.title}</h4>
                                            <h5 class="text-sky-900 font-bold text-base mb-2">{cert.issued}</h5>

                                            {/* SECONDARY DATA: Issuing organization, location (smaller and less prominent) */}
                                            <div class="text-sm text-slate-600 space-y-1">
                                                <p>
                                                    <span class="font-medium">Issuer:</span> {cert.issuer}
                                                </p>
                                                <p>
                                                    <span class="font-medium">Location:</span> {cert.location}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            );
                        })}
                    </div>
                </section>
            )
        }
    </div>
</section>
